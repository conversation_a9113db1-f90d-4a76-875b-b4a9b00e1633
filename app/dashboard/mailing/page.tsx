"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { MailingListManager } from "@/components/MailingListManager"

export default function MailingListDashboard() {
  const [profile, setProfile] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkAuthAndFetchData()
  }, [])

  const checkAuthAndFetchData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        router.push('/login')
        return
      }

      // Get user profile
      const { data: userProfile, error: profileError } = await supabase
        .from("users")
        .select(`
          id,
          name,
          custom_url,
          role
        `)
        .eq("id", user.id)
        .single()

      if (profileError) {
        console.error("Error fetching profile:", profileError)
        return
      }

      if (!userProfile) {
        router.push('/login')
        return
      }

      // Check if user is a writer/admin
      if (userProfile.role !== 'writer' && userProfile.role !== 'admin') {
        router.push('/dashboard')
        return
      }

      setProfile(userProfile)
    } catch (error) {
      console.error("Error in checkAuthAndFetchData:", error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading mailing list...</p>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Unable to load profile</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6">
          <Link 
            href="/dashboard" 
            className="text-purple-600 hover:text-purple-700 font-medium mb-4 inline-block"
          >
            ← Back to Dashboard
          </Link>
          <h1 className="text-2xl font-serif text-gray-900 mb-2">
            Mailing List Management
          </h1>
          <p className="text-gray-600">
            Manage your subscribers and send notifications
          </p>
        </div>

        {/* Mailing List Manager */}
        <MailingListManager 
          creatorId={profile.id}
          creatorName={profile.name}
          customUrl={profile.custom_url}
        />
      </div>
    </div>
  )
}
